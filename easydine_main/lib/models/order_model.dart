import 'package:equatable/equatable.dart';

/// Response wrapper for orders API
class OrdersApiResponse extends Equatable {
  final int statusCode;
  final bool success;
  final String message;
  final OrdersData data;

  const OrdersApiResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    required this.data,
  });

  factory OrdersApiResponse.fromJson(Map<String, dynamic> json) {
    return OrdersApiResponse(
      statusCode: json['statusCode'] ?? 200,
      success: json['success'] ?? true,
      message: json['message'] ?? '',
      data: OrdersData.fromJson(json['data'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [statusCode, success, message, data];
}

/// Orders data with pagination
class OrdersData extends Equatable {
  final List<OrderDetail> data;
  final Pagination pagination;

  const OrdersData({
    required this.data,
    required this.pagination,
  });

  factory OrdersData.fromJson(Map<String, dynamic> json) {
    return OrdersData(
      data: (json['data'] as List<dynamic>? ?? [])
          .map((item) => OrderDetail.fromJson(item))
          .toList(),
      pagination: Pagination.fromJson(json['pagination'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [data, pagination];
}

/// Pagination information
class Pagination extends Equatable {
  final int page;
  final int limit;
  final int total;
  final int totalPages;

  const Pagination({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 10,
      total: json['total'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [page, limit, total, totalPages];
}

/// Main order detail model
class OrderDetail extends Equatable {
  final String orderDetailId;
  final String orderCode;
  final String? table;
  final String total;
  final String status;
  final int numberOfPeople;
  final OrderType orderType;
  final String orderApproval;
  final int? estimatedPrepTime;
  final DateTime? prepStartTime;
  final DateTime? readyTime;
  final DateTime? servedTime;
  final Staff? assignedChef;
  final Staff? assignedWaiter;
  final Staff? orderedBy;
  final Customer? customerInfo;
  final String branchId;
  final String notes;
  final List<dynamic> miscItems;
  final List<dynamic> alerts;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<OrderItem> orderItems;
  final Customer? customer;

  const OrderDetail({
    required this.orderDetailId,
    required this.orderCode,
    this.table,
    required this.total,
    required this.status,
    required this.numberOfPeople,
    required this.orderType,
    required this.orderApproval,
    this.estimatedPrepTime,
    this.prepStartTime,
    this.readyTime,
    this.servedTime,
    this.assignedChef,
    this.assignedWaiter,
    this.orderedBy,
    this.customerInfo,
    required this.branchId,
    required this.notes,
    required this.miscItems,
    required this.alerts,
    required this.createdAt,
    required this.updatedAt,
    required this.orderItems,
    this.customer,
  });

  factory OrderDetail.fromJson(Map<String, dynamic> json) {
    return OrderDetail(
      orderDetailId: json['orderDetailId'] ?? '',
      orderCode: json['orderCode'] ?? '',
      table: json['table'],
      total: json['total'] ?? '0.00',
      status: json['status'] ?? '',
      numberOfPeople: json['numberOfPeople'] ?? 1,
      orderType: _parseOrderType(json['orderType']),
      orderApproval: json['orderApproval'] ?? '',
      estimatedPrepTime: json['estimatedPrepTime'],
      prepStartTime: json['prepStartTime'] != null
          ? DateTime.parse(json['prepStartTime'])
          : null,
      readyTime:
          json['readyTime'] != null ? DateTime.parse(json['readyTime']) : null,
      servedTime: json['servedTime'] != null
          ? DateTime.parse(json['servedTime'])
          : null,
      assignedChef: _parseStaff(json['assignedChef']),
      assignedWaiter: _parseStaff(json['assignedWaiter']),
      orderedBy: _parseStaff(json['orderedBy']),
      customerInfo: _parseCustomer(json['customerInfo']),
      branchId: json['branchId'] ?? '',
      notes: json['notes'] ?? '',
      miscItems: json['miscItems'] ?? [],
      alerts: json['alerts'] ?? [],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      orderItems: (json['orderItems'] as List<dynamic>? ?? [])
          .map((item) => OrderItem.fromJson(item))
          .toList(),
      customer: _parseCustomer(json['customer']),
    );
  }

  /// Helper method to parse orderType from either Map or String
  static OrderType _parseOrderType(dynamic orderTypeData) {
    if (orderTypeData == null) {
      return const OrderType(id: '', name: 'Unknown');
    }

    if (orderTypeData is Map<String, dynamic>) {
      return OrderType.fromJson(orderTypeData);
    }

    if (orderTypeData is String) {
      return OrderType(id: '', name: orderTypeData);
    }

    // Fallback for any other type
    return const OrderType(id: '', name: 'Unknown');
  }

  /// Helper method to parse staff from either Map or other types
  static Staff? _parseStaff(dynamic staffData) {
    if (staffData == null) {
      return null;
    }

    if (staffData is Map<String, dynamic>) {
      return Staff.fromJson(staffData);
    }

    // Fallback for any other type
    return null;
  }

  /// Helper method to parse customer from either Map or other types
  static Customer? _parseCustomer(dynamic customerData) {
    if (customerData == null) {
      return null;
    }

    if (customerData is Map<String, dynamic>) {
      return Customer.fromJson(customerData);
    }

    // Fallback for any other type
    return null;
  }

  @override
  List<Object?> get props => [
        orderDetailId,
        orderCode,
        table,
        total,
        status,
        numberOfPeople,
        orderType,
        orderApproval,
        estimatedPrepTime,
        prepStartTime,
        readyTime,
        servedTime,
        assignedChef,
        assignedWaiter,
        orderedBy,
        customerInfo,
        branchId,
        notes,
        miscItems,
        alerts,
        createdAt,
        updatedAt,
        orderItems,
        customer
      ];
}

/// Order type model
class OrderType extends Equatable {
  final String id;
  final String name;

  const OrderType({
    required this.id,
    required this.name,
  });

  factory OrderType.fromJson(Map<String, dynamic> json) {
    return OrderType(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
    );
  }

  @override
  List<Object?> get props => [id, name];
}

/// Staff model
class Staff extends Equatable {
  final String id;
  final String name;

  const Staff({
    required this.id,
    required this.name,
  });

  factory Staff.fromJson(Map<String, dynamic> json) {
    return Staff(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
    );
  }

  @override
  List<Object?> get props => [id, name];
}

/// Customer model
class Customer extends Equatable {
  final String? id;
  final String? name;
  final String? phoneNumber;
  final String? email;

  const Customer({
    this.id,
    this.name,
    this.phoneNumber,
    this.email,
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'],
      name: json['name'],
      phoneNumber: json['phoneNumber'],
      email: json['email'],
    );
  }

  @override
  List<Object?> get props => [id, name, phoneNumber, email];
}

/// Order item model
class OrderItem extends Equatable {
  final String orderItemId;
  final String name;
  final String price;
  final int quantity;
  final String type;
  final BaseItem baseItem;
  final List<Allergy>? allergies;
  final List<dynamic>? dishSizes;
  final List<dynamic>? dishExclusions;
  final List<dynamic>? cookingStyles;
  final String? spiciness;
  final List<DishAddon>? dishAddons;
  final List<DishExtra>? dishExtras;
  final List<dynamic>? dishSides;
  final List<dynamic>? dishBeverages;
  final List<dynamic>? dishDesserts;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Legacy properties for backward compatibility
  String get id => orderItemId;
  Map<String, dynamic>? get customization => {
        'allergies':
            allergies?.map((a) => {'id': a.id, 'name': a.name}).toList(),
        'dishAddons': dishAddons
            ?.map((a) => {
                  'id': a.id,
                  'name': a.name,
                  'price': a.price,
                  'quantity': a.quantity
                })
            .toList(),
        'dishExtras': dishExtras
            ?.map((e) => {
                  'id': e.id,
                  'name': e.name,
                  'price': e.price,
                  'quantity': e.quantity
                })
            .toList(),
        'spiciness': spiciness,
        'notes': notes,
      };

  const OrderItem({
    required this.orderItemId,
    required this.name,
    required this.price,
    required this.quantity,
    required this.type,
    required this.baseItem,
    this.allergies,
    this.dishSizes,
    this.dishExclusions,
    this.cookingStyles,
    this.spiciness,
    this.dishAddons,
    this.dishExtras,
    this.dishSides,
    this.dishBeverages,
    this.dishDesserts,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      orderItemId: json['orderItemId'] ?? '',
      name: json['name'] ?? '',
      price: json['price'] ?? '0.00',
      quantity: json['quantity'] ?? 1,
      type: json['type'] ?? '',
      baseItem: _parseBaseItem(json['baseItem']),
      allergies: json['allergies'] != null
          ? (json['allergies'] as List<dynamic>)
              .map((item) => Allergy.fromJson(item))
              .toList()
          : null,
      dishSizes: json['dishSizes'],
      dishExclusions: json['dishExclusions'],
      cookingStyles: json['cookingStyles'],
      spiciness: json['spiciness'],
      dishAddons: json['dishAddons'] != null
          ? (json['dishAddons'] as List<dynamic>)
              .map((item) => DishAddon.fromJson(item))
              .toList()
          : null,
      dishExtras: json['dishExtras'] != null
          ? (json['dishExtras'] as List<dynamic>)
              .map((item) => DishExtra.fromJson(item))
              .toList()
          : null,
      dishSides: json['dishSides'],
      dishBeverages: json['dishBeverages'],
      dishDesserts: json['dishDesserts'],
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Helper method to parse baseItem from either Map or other types
  static BaseItem _parseBaseItem(dynamic baseItemData) {
    if (baseItemData == null) {
      return const BaseItem(id: '', name: 'Unknown', price: '0.00');
    }

    if (baseItemData is Map<String, dynamic>) {
      return BaseItem.fromJson(baseItemData);
    }

    // Fallback for any other type
    return const BaseItem(id: '', name: 'Unknown', price: '0.00');
  }

  @override
  List<Object?> get props => [
        orderItemId,
        name,
        price,
        quantity,
        type,
        baseItem,
        allergies,
        dishSizes,
        dishExclusions,
        cookingStyles,
        spiciness,
        dishAddons,
        dishExtras,
        dishSides,
        dishBeverages,
        dishDesserts,
        notes,
        createdAt,
        updatedAt
      ];
}

/// Base item model
class BaseItem extends Equatable {
  final String id;
  final String name;
  final String price;

  const BaseItem({
    required this.id,
    required this.name,
    required this.price,
  });

  factory BaseItem.fromJson(Map<String, dynamic> json) {
    return BaseItem(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      price: json['price'] ?? '0.00',
    );
  }

  @override
  List<Object?> get props => [id, name, price];
}

/// Allergy model
class Allergy extends Equatable {
  final String id;
  final String name;

  const Allergy({
    required this.id,
    required this.name,
  });

  factory Allergy.fromJson(Map<String, dynamic> json) {
    return Allergy(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
    );
  }

  @override
  List<Object?> get props => [id, name];
}

/// Dish addon model
class DishAddon extends Equatable {
  final String id;
  final String name;
  final double price;
  final int quantity;

  const DishAddon({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
  });

  factory DishAddon.fromJson(Map<String, dynamic> json) {
    return DishAddon(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      quantity: json['quantity'] ?? 1,
    );
  }

  @override
  List<Object?> get props => [id, name, price, quantity];
}

/// Dish extra model
class DishExtra extends Equatable {
  final String id;
  final String name;
  final double price;
  final int quantity;

  const DishExtra({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
  });

  factory DishExtra.fromJson(Map<String, dynamic> json) {
    return DishExtra(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      quantity: json['quantity'] ?? 1,
    );
  }

  @override
  List<Object?> get props => [id, name, price, quantity];
}

/// Legacy OrderModel for backward compatibility
class OrderModel extends Equatable {
  final String id;
  final String tableId;
  final double total;
  final String status;
  final DateTime createdAt;
  final List<OrderItem> items;

  const OrderModel({
    required this.id,
    required this.tableId,
    required this.total,
    required this.status,
    required this.createdAt,
    required this.items,
  });

  /// Convert from OrderDetail to legacy OrderModel
  factory OrderModel.fromOrderDetail(OrderDetail orderDetail) {
    return OrderModel(
      id: orderDetail.orderDetailId,
      tableId: orderDetail.table ?? 'Unknown',
      total: double.tryParse(orderDetail.total) ?? 0.0,
      status: orderDetail.status,
      createdAt: orderDetail.createdAt,
      items: orderDetail.orderItems,
    );
  }

  @override
  List<Object?> get props => [id, tableId, total, status, createdAt, items];
}
